[gd_scene load_steps=8 format=3 uid="uid://2duciswm4agd"]

[ext_resource type="Texture2D" uid="uid://d2foipga3wwr0" path="res://resources/solaria/planting/ground_prepared_seeded.png" id="1_ejssu"]
[ext_resource type="Texture2D" uid="uid://ca8d0iyi4fely" path="res://resources/solaria/planting/plant_growing_stages.png" id="2_lode1"]
[ext_resource type="PackedScene" uid="uid://otpfc634hhga" path="res://scenes/UI/progress/ProgressBar.tscn" id="3_jsxeq"]
[ext_resource type="Texture2D" uid="uid://bsr271jhq50ei" path="res://resources/solaria/UI/progress/progressFrontGreenHorizontal.png" id="4_1vq43"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_b17rq"]
size = Vector2(16, 16)

[sub_resource type="Animation" id="Animation_jsxeq"]
resource_name = "Ready"

[sub_resource type="AnimationLibrary" id="AnimationLibrary_1vq43"]
_data = {
&"Ready": SubResource("Animation_jsxeq")
}

[node name="Plant" type="Node2D"]

[node name="Ground" type="Sprite2D" parent="."]
texture = ExtResource("1_ejssu")

[node name="Plant" type="Sprite2D" parent="."]
position = Vector2(0, -8)
texture = ExtResource("2_lode1")
hframes = 5
vframes = 48

[node name="PlayerDetector" type="Area2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="PlayerDetector"]
shape = SubResource("RectangleShape2D_b17rq")

[node name="ProgressBar" parent="." instance=ExtResource("3_jsxeq")]
position = Vector2(0, 7)
scale = Vector2(1, 0.4)
FrontTexture = ExtResource("4_1vq43")

[node name="ReadyPlant" type="Sprite2D" parent="."]

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_1vq43")
}
